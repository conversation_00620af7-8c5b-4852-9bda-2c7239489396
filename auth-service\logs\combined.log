{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-20 08:21:07"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:21:07"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:11","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:11","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:22:32","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:25:23","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-20 08:29:22"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:29:22"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:30:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:30:08"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:30:08","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:30:08","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","hasProfile":true,"ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-20 08:30:09","updatedFields":["username","full_name","school_id","date_of_birth","gender"],"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:30:09","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:30:09","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"ip":"::1","level":"info","message":"User profile deleted","service":"auth-service","timestamp":"2025-07-20 08:30:39","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-20 08:46:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:46:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:46:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:46:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:47:31"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:47:31"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:47:31","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:47:31","userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"email":"<EMAIL>","hasProfile":true,"ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-20 08:47:31","updatedFields":["username","full_name","school_id","date_of_birth","gender"],"userId":"19b3fa08-84b6-4c5d-bf4b-0f156d8485f6"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:47:58"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:47:58"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 08:47:58"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"52a8c758-361a-478c-afff-5329e77bf785","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 08:47:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 08:47:58","tokenBalance":3,"userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 08:47:58","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"error":"Validation error","level":"error","message":"Error updating user profile","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:47:58","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"error":"Validation error","ip":"::1","level":"error","message":"Unhandled error occurred","method":"PUT","path":"/auth/profile","requestId":"58796c10-4ac8-446d-8695-5636168c79d2","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:47:58","userAgent":"axios/1.10.0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:47:58","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:47:58","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:49:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:49:36"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:49:36","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-20 08:49:36","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"error":"Validation error","level":"error","message":"Error updating user profile","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:49:36","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"error":"Validation error","ip":"::1","level":"error","message":"Unhandled error occurred","method":"PUT","path":"/auth/profile","requestId":"4b2b8460-2b9f-4077-bdb2-a4b63ab3a8a3","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:49:36","userAgent":"axios/1.10.0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:49:36","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 08:49:36","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-20 08:58:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 08:58:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:06:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:06:14"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:06:14"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"48c6dddf-2f23-4032-b4d6-7426f2434d26","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:06:14","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:06:14","tokenBalance":3,"userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:06:14","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
{"error":"Validation error","level":"error","message":"Error updating user profile","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 09:06:14","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
{"error":"Validation error","ip":"::1","level":"error","message":"Unhandled error occurred","method":"PUT","path":"/auth/profile","requestId":"7fd747ad-00bc-44cd-ac7f-d9702fdc589e","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 09:06:14","userAgent":"axios/1.10.0"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:06:14","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:06:14","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:09:10"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:09:10"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:09:10"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"40b3b4d2-0681-4183-8e60-b12bc406001d","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:09:10","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:09:10","tokenBalance":3,"userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:09:10","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6"}
{"errors":["Username must contain only alphanumeric characters"],"level":"warn","message":"Request validation failed","method":"PUT","path":"/profile","service":"auth-service","timestamp":"2025-07-20 09:09:10"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:09:10","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:09:10","userId":"d664b357-1ad1-4b24-a08b-a39d2f2cfcb6"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:26:16"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:26:16"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:26:25","tokenBalance":3,"userId":"6ccfb955-f22e-4185-9078-bc802001e788"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:26:25","userId":"6ccfb955-f22e-4185-9078-bc802001e788"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:26:32"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"95476d03-0e48-4330-8ba7-618bf2e645e0","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:26:32","userAgent":"axios/1.10.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:27:47"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:27:47"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:27:47"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"e0d684ab-31f7-407f-ae7c-29b03839a9a4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:27:47","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:27:47","tokenBalance":3,"userId":"0f6562fd-aabf-49e1-938a-6e1ebf788c7a"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:27:47","userId":"0f6562fd-aabf-49e1-938a-6e1ebf788c7a"}
{"errors":["Username must contain only alphanumeric characters"],"level":"warn","message":"Request validation failed","method":"PUT","path":"/profile","service":"auth-service","timestamp":"2025-07-20 09:27:47"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:27:47","userId":"0f6562fd-aabf-49e1-938a-6e1ebf788c7a"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:27:47","userId":"0f6562fd-aabf-49e1-938a-6e1ebf788c7a"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:30:27"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:30:27"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:30:27","tokenBalance":3,"userId":"ed90a317-65f8-481e-8975-0e94c7f908d3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:30:27","userId":"ed90a317-65f8-481e-8975-0e94c7f908d3"}
{"email":"<EMAIL>","hasProfile":true,"ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-20 09:30:27","updatedFields":["username","full_name","school_id","date_of_birth","gender"],"userId":"ed90a317-65f8-481e-8975-0e94c7f908d3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:30:27","userId":"ed90a317-65f8-481e-8975-0e94c7f908d3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:30:27","userId":"ed90a317-65f8-481e-8975-0e94c7f908d3"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:33:25"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-20 09:33:25"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:33:25","tokenBalance":3,"userId":"c244d482-6f12-4d31-99d5-e0f3f6ebf2a5"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-20 09:33:25","userId":"c244d482-6f12-4d31-99d5-e0f3f6ebf2a5"}
{"email":"<EMAIL>","hasProfile":true,"ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-20 09:33:25","updatedFields":["username","full_name","school_id","date_of_birth","gender"],"userId":"c244d482-6f12-4d31-99d5-e0f3f6ebf2a5"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:33:25","userId":"c244d482-6f12-4d31-99d5-e0f3f6ebf2a5"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-20 09:33:25","userId":"c244d482-6f12-4d31-99d5-e0f3f6ebf2a5"}
{"ip":"::1","level":"info","message":"User profile deleted","service":"auth-service","timestamp":"2025-07-20 09:33:47","userId":"c244d482-6f12-4d31-99d5-e0f3f6ebf2a5"}
