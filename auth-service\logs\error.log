{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 08:47:58"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"52a8c758-361a-478c-afff-5329e77bf785","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 08:47:58","userAgent":"axios/1.10.0"}
{"error":"Validation error","level":"error","message":"Error updating user profile","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:47:58","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"error":"Validation error","ip":"::1","level":"error","message":"Unhandled error occurred","method":"PUT","path":"/auth/profile","requestId":"58796c10-4ac8-446d-8695-5636168c79d2","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:47:58","userAgent":"axios/1.10.0"}
{"error":"Validation error","level":"error","message":"Error updating user profile","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:49:36","userId":"8dd66441-da32-47ba-b532-d4fd25b82162"}
{"error":"Validation error","ip":"::1","level":"error","message":"Unhandled error occurred","method":"PUT","path":"/auth/profile","requestId":"4b2b8460-2b9f-4077-bdb2-a4b63ab3a8a3","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 08:49:36","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:06:14"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"48c6dddf-2f23-4032-b4d6-7426f2434d26","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:06:14","userAgent":"axios/1.10.0"}
{"error":"Validation error","level":"error","message":"Error updating user profile","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 09:06:14","userId":"fa7b0620-901b-4c0c-859d-adede356a3dd"}
{"error":"Validation error","ip":"::1","level":"error","message":"Unhandled error occurred","method":"PUT","path":"/auth/profile","requestId":"7fd747ad-00bc-44cd-ac7f-d9702fdc589e","service":"auth-service","stack":"Error\n    at Query.run (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async model.update (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\model.js:2598:12)\n    at async updateProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\userController.js:138:9)","timestamp":"2025-07-20 09:06:14","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:09:10"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"40b3b4d2-0681-4183-8e60-b12bc406001d","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:09:10","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:26:32"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"95476d03-0e48-4330-8ba7-618bf2e645e0","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:26:32","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-20 09:27:47"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"e0d684ab-31f7-407f-ae7c-29b03839a9a4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:189:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:112:20)","timestamp":"2025-07-20 09:27:47","userAgent":"axios/1.10.0"}
